Below is a plain text version that contains everything in one complete document without any Markdown formatting:

------------------------------------------------------------
LLM Prompt: Build a Go-Based GitHub Webhook Deployment Orchestrator

Prompt for LLM:
----------------
You are to design and implement a robust, production-ready deployment orchestrator in Go. This service will:
– Listen for GitHub webhook events (primarily push events)
– Map repositories to local folders
– Execute configurable deployment commands per application

The orchestrator must be highly configurable via a .env (or configuration) file, secure, and support advanced features such as:
• Concurrency control (with per-app queuing to avoid overlapping deployments)
• Timeouts for commands or the overall process
• Rollback (advanced) if a deployment fails
• Manual API triggers via an authenticated HTTP endpoint

Additionally, the orchestrator must log all deployment activity in detail, including events like start, success, failure, rollback, and timeout. It must enforce a locking mechanism to prevent simultaneous deployments for the same app.

Your output should include:
– A clear Go project structure
– The main Go code implementing the features below
– An example .env (or configuration) file
– Example log output
– Explanations for any non-obvious design decisions

------------------------------------------------------------
Required Features:
------------------
1. Webhook Listener
   • Listens for GitHub webhook payloads (e.g., push events)
   • Parses the payload for repository information (such as the repository’s full_name), branch, commit, etc.

2. Repository-to-Folder Mapping
   • Maps the repository’s full_name (e.g., "octocat/Hello-World") to a local folder (e.g., "~/Hello-World")
   • Supports custom mappings when the folder name differs from the repo name

3. Per-App Command Configuration
   • Allows specifying custom shell commands for each app
   • Uses default commands as a fallback if no per-app configuration is provided

4. Configuration via .env or Config File
   • Centralizes all settings (port, secrets, repo mappings, commands, etc.) for easy editing

5. Security
   • Verifies the webhook secret to ensure payload authenticity
   • Optionally includes an IP allowlist for extra protection

6. Logging
   • Logs all deployments, errors, and webhook events
   • Deployment Status Tracking: Records each deployment event (start, success, failure, rollback, timeout) with details such as timestamp, repo, branch, commit, status, and any error messages

7. Notifications (Optional)
   • Sends notifications (e.g., by email, Slack) on deployment success, failure, or rollback

8. Branch Filtering
   • Triggers deployments only if the push event is from specific branches (for example, the "main" branch)

9. Dry Run/Test Mode
   • Provides a mode for testing the configuration without actually executing deployment commands

10. Health Check Endpoint
    • Offers an endpoint to report the service's health for monitoring purposes

11. Concurrency
    • Allows parallel deployments for different apps
    • Queues deployments for a single app to avoid overlapping execution
    • Supports configurable concurrency limits

12. Timeouts
    • Sets a maximum execution time for each deployment command or the entire process
    • Terminates commands and marks the deployment as failed if a timeout occurs

13. Rollback (Advanced)
    • Runs a rollback command if a deployment fails (for example, "git reset --hard <previous_commit>" or a custom script)
    • Stores the previous state/commit hash before deploying, so that rollback is possible
    • Optionally sends a notification on rollback

14. API for Manual Trigger
    • Exposes an authenticated HTTP API endpoint (e.g., "/deploy?repo=octocat/Hello-World") to manually trigger deployments
    • Requires an API key or token for authentication
    • Allows specifying branch, commit, or custom commands via the API

15. Locking Mechanism
    • Prevents overlapping deployments for the same app by implementing a per-app lock
    • Queues or rejects new deployment requests for an app until the current deployment has completed

------------------------------------------------------------
Configuration & Examples:
----------------------------
Below is an example configuration file along with sample deployment log output.

Example .env Configuration:
---------------------------
PORT=3000
WEBHOOK_SECRET=yoursecret
API_KEY=your_api_key_here

# Logging
LOG_FILE=/var/log/deployer.log

# Repo-to-Folder Mapping (comma-separated)
REPO_MAP=octocat/Hello-World:~/Hello-World,octocat/Other:~/OtherApp

# Per-App Commands
COMMANDS_Hello-World=git pull && pnpm i && pnpm build && pm2 start hello-world
COMMANDS_Other=git pull && npm ci && npm run build && pm2 start other

# Default Commands (if not overridden by per-app settings)
DEFAULT_COMMANDS=git pull && pnpm i && pnpm build && pm2 start appname

# Branch Filter (only deploy on this branch)
BRANCH_FILTER=main

# Concurrency and Timeouts
CONCURRENCY_LIMIT=2
TIMEOUT_SECONDS=300

# Rollback Commands (per app)
ROLLBACK_COMMANDS_Hello-World=git reset --hard HEAD~1 && pm2 restart hello-world

# Notifications (optional)
NOTIFY_ON_ROLLBACK=true

Example Deployment Log Output:
------------------------------
2025-06-24T10:15:00Z | Hello-World | main | 1481a2de7b2a7d02428ad93446ab166be7793fbb | STARTED
2025-06-24T10:15:10Z | Hello-World | main | 1481a2de7b2a7d02428ad93446ab166be7793fbb | SUCCESS
2025-06-24T10:16:00Z | Hello-World | main | 1481a2de7b2a7d02428ad93446ab166be7793fbb | FAILED | error: build failed

------------------------------------------------------------
Summary of Features:
--------------------
- Webhook Listener: Listens for GitHub events and parses the payload.
- Repository-to-Folder Mapping: Maps a repository (using its full_name) to a designated local folder; supports custom mappings.
- Per-App Commands: Allows custom deployment commands per app, with a default fallback.
- Configuration File: Central location for all settings (port, secrets, mappings, commands, etc.).
- Security: Ensures payload authenticity through webhook secret verification and optional IP allowlisting.
- Logging: Records detailed events including deployment status (start, success, failure, rollback, timeout) in a log file.
- Notifications: Optionally sends alerts on deployment outcomes (success, failure, rollback).
- Branch Filtering: Deploys changes only from specified branches.
- Dry Run/Test Mode: Allows for configuration testing without executing commands.
- Health Check: Provides a monitoring endpoint for the service.
- Concurrency: Enables parallel deployments for different apps and queues deployments per app to prevent overlap.
- Timeouts: Defines maximum execution times to prevent hanging deployments.
- Rollback: Executes a rollback command upon failure and optionally notifies on rollback occurrences.
- Manual API: Offers an authenticated HTTP endpoint for manually triggering deployments.
- Locking Mechanism: Prevents simultaneous deployments for the same app via per-app locks.

------------------------------------------------------------
Please generate the Go code, project structure, and documentation for this deployment orchestrator based on the above specifications.
------------------------------------------------------------

You can now copy and paste this complete text into an LLM or use it as a project specification document.

Let me know if you need any further modifications!