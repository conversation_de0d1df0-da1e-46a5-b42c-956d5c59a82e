# Server Configuration
PORT=3000
WEBHOOK_SECRET=yoursecret
API_KEY=your_api_key_here

# Logging
LOG_FILE=/var/log/deployer.log

# Repo-to-Folder Mapping (comma-separated)
# Format: repo1:path1,repo2:path2
REPO_MAP=octocat/Hello-World:~/Hello-World,octocat/Other:~/OtherApp

# Per-App Commands
# Format: COMMANDS_<app-name>=<command>
COMMANDS_Hello-World=git pull && pnpm i && pnpm build && pm2 start hello-world
COMMANDS_Other=git pull && npm ci && npm run build && pm2 start other

# Default Commands (if not overridden by per-app settings)
DEFAULT_COMMANDS=git pull && pnpm i && pnpm build && pm2 start appname

# Branch Filter (only deploy on this branch)
BRANCH_FILTER=main

# Concurrency and Timeouts
CONCURRENCY_LIMIT=2
TIMEOUT_SECONDS=300

# Rollback Commands (per app)
# Format: ROLL<PERSON>CK_COMMANDS_<app-name>=<command>
ROLL<PERSON>CK_COMMANDS_Hello-World=git reset --hard HEAD~1 && pm2 restart hello-world

# Notifications (optional)
NOTIFY_ON_ROLLBACK=true

# Security (optional)
# IP_ALLOWLIST=***********/24,10.0.0.0/8

# Features
DRY_RUN=false
